import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FirebaseService } from '../../services/firebase.service';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatCheckboxModule
  ],
  template: `
    <div class="register-container">
      <div class="register-content">
        <mat-card class="register-card">
          <mat-card-header class="register-header">
            <div class="header-content">
              <mat-icon class="register-icon">smart_toy</mat-icon>
              <mat-card-title>Create Account</mat-card-title>
              <mat-card-subtitle>Join Abid Ansari AI Assistant today</mat-card-subtitle>
            </div>
          </mat-card-header>

          <mat-card-content>
            <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Display Name</mat-label>
                <input matInput type="text" formControlName="displayName" placeholder="Enter your name">
                <mat-icon matSuffix>person</mat-icon>
                <mat-error *ngIf="registerForm.get('displayName')?.hasError('required')">
                  Display name is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput type="email" formControlName="email" placeholder="Enter your email">
                <mat-icon matSuffix>email</mat-icon>
                <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
                  Email is required
                </mat-error>
                <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
                  Please enter a valid email
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Password</mat-label>
                <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" placeholder="Create a password">
                <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                  <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
                  Password is required
                </mat-error>
                <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
                  Password must be at least 6 characters
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Confirm Password</mat-label>
                <input matInput [type]="hideConfirmPassword ? 'password' : 'text'" formControlName="confirmPassword" placeholder="Confirm your password">
                <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
                  <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
                  Please confirm your password
                </mat-error>
                <mat-error *ngIf="registerForm.hasError('passwordMismatch') && registerForm.get('confirmPassword')?.touched">
                  Passwords do not match
                </mat-error>
              </mat-form-field>

              <mat-checkbox formControlName="acceptTerms" class="terms-checkbox">
                I agree to the <a routerLink="/terms" target="_blank">Terms of Service</a> and <a routerLink="/privacy" target="_blank">Privacy Policy</a>
              </mat-checkbox>

              <div class="pricing-info">
                <div class="pricing-card">
                  <mat-icon>monetization_on</mat-icon>
                  <div class="pricing-details">
                    <h4>Pricing</h4>
                    <p>{{ pricing.currency }}{{ pricing.sessionPrice }} per interview session</p>
                    <small>Pay only for what you use</small>
                  </div>
                </div>
              </div>

              <div class="form-actions">
                <button mat-raised-button color="primary" type="submit" [disabled]="registerForm.invalid || isLoading" class="register-button">
                  <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
                  <span *ngIf="!isLoading">Create Account</span>
                </button>
              </div>
            </form>
          </mat-card-content>

          <mat-card-actions class="register-actions">
            <div class="action-links">
              <p>Already have an account? <a routerLink="/login" class="link">Sign in here</a></p>
            </div>
          </mat-card-actions>
        </mat-card>

        <div class="benefits-preview">
          <h3>What You Get</h3>
          <div class="benefit-list">
            <div class="benefit-item">
              <mat-icon>check_circle</mat-icon>
              <div>
                <h4>Secure API Key Management</h4>
                <p>Store and manage your AI API keys securely with encryption</p>
              </div>
            </div>
            <div class="benefit-item">
              <mat-icon>check_circle</mat-icon>
              <div>
                <h4>Multiple AI Models</h4>
                <p>Access to Gemini, Mistral, OpenAI, and OpenRouter APIs</p>
              </div>
            </div>
            <div class="benefit-item">
              <mat-icon>check_circle</mat-icon>
              <div>
                <h4>Advanced Features</h4>
                <p>Screenshot analysis, hotkey controls, and stealth mode</p>
              </div>
            </div>
            <div class="benefit-item">
              <mat-icon>check_circle</mat-icon>
              <div>
                <h4>Technical Interview Support</h4>
                <p>Specialized AI assistance for coding and system design</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .register-container {
      min-height: 100vh;
      padding-top: 64px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .register-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 48px;
      max-width: 1200px;
      width: 100%;
      padding: 24px;
      align-items: start;
    }

    .register-card {
      max-width: 500px;
      width: 100%;
      margin: 0 auto;
    }

    .register-header {
      text-align: center;
      padding-bottom: 24px;
    }

    .header-content {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .register-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #3f51b5;
      margin-bottom: 16px;
    }

    .register-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .full-width {
      width: 100%;
    }

    .terms-checkbox {
      margin: 16px 0;
    }

    .terms-checkbox a {
      color: #3f51b5;
      text-decoration: none;
    }

    .terms-checkbox a:hover {
      text-decoration: underline;
    }

    .pricing-info {
      margin: 16px 0;
    }

    .pricing-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
      border-radius: 8px;
      border-left: 4px solid #4caf50;
    }

    .pricing-card mat-icon {
      color: #4caf50;
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .pricing-details h4 {
      margin: 0 0 4px 0;
      color: #2e7d32;
      font-weight: 600;
    }

    .pricing-details p {
      margin: 0 0 4px 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #2e7d32;
    }

    .pricing-details small {
      color: #666;
    }

    .form-actions {
      margin-top: 16px;
    }

    .register-button {
      width: 100%;
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      position: relative;
    }

    .button-spinner {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    .register-actions {
      padding-top: 16px;
    }

    .action-links {
      text-align: center;
      width: 100%;
    }

    .action-links p {
      margin-bottom: 8px;
      color: #666;
    }

    .link {
      color: #3f51b5;
      text-decoration: none;
      font-weight: 500;
    }

    .link:hover {
      text-decoration: underline;
    }

    .benefits-preview {
      background: white;
      padding: 32px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      position: sticky;
      top: 80px;
    }

    .benefits-preview h3 {
      color: #333;
      margin-bottom: 24px;
      font-size: 1.5rem;
      text-align: center;
    }

    .benefit-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .benefit-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    .benefit-item mat-icon {
      color: #4caf50;
      font-size: 24px;
      width: 24px;
      height: 24px;
      margin-top: 2px;
    }

    .benefit-item h4 {
      margin: 0 0 4px 0;
      color: #333;
      font-weight: 600;
    }

    .benefit-item p {
      margin: 0;
      color: #666;
      line-height: 1.4;
    }

    @media (max-width: 768px) {
      .register-content {
        grid-template-columns: 1fr;
        gap: 24px;
        padding: 16px;
      }

      .benefits-preview {
        order: -1;
        position: static;
      }

      .register-card {
        max-width: 100%;
      }
    }
  `]
})
export class RegisterComponent {
  registerForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  hideConfirmPassword = true;
  pricing = environment.pricing;

  constructor(
    private fb: FormBuilder,
    private firebaseService: FirebaseService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.registerForm = this.fb.group({
      displayName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      acceptTerms: [false, [Validators.requiredTrue]]
    }, { validators: this.passwordMatchValidator });
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  async onSubmit(): Promise<void> {
    if (this.registerForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      try {
        const { email, password, displayName } = this.registerForm.value;
        const result = await this.firebaseService.register(email, password, displayName);
        
        if (result.success) {
          this.snackBar.open('Account created successfully!', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard']);
        } else {
          this.snackBar.open(result.message, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      } catch (error) {
        this.snackBar.open('An error occurred during registration', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      } finally {
        this.isLoading = false;
      }
    }
  }
}
