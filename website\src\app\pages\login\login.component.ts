import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FirebaseService } from '../../services/firebase.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <div class="login-container">
      <div class="login-content">
        <mat-card class="login-card">
          <mat-card-header class="login-header">
            <div class="header-content">
              <mat-icon class="login-icon">smart_toy</mat-icon>
              <mat-card-title>Welcome Back</mat-card-title>
              <mat-card-subtitle>Sign in to your Abid Ansari AI Assistant account</mat-card-subtitle>
            </div>
          </mat-card-header>

          <mat-card-content>
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput type="email" formControlName="email" placeholder="Enter your email">
                <mat-icon matSuffix>email</mat-icon>
                <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                  Email is required
                </mat-error>
                <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                  Please enter a valid email
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Password</mat-label>
                <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" placeholder="Enter your password">
                <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                  <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                  Password is required
                </mat-error>
              </mat-form-field>

              <div class="form-actions">
                <button mat-raised-button color="primary" type="submit" [disabled]="loginForm.invalid || isLoading" class="login-button">
                  <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
                  <span *ngIf="!isLoading">Sign In</span>
                </button>
              </div>
            </form>
          </mat-card-content>

          <mat-card-actions class="login-actions">
            <div class="action-links">
              <p>Don't have an account? <a routerLink="/register" class="link">Create one here</a></p>
              <a routerLink="/forgot-password" class="link">Forgot your password?</a>
            </div>
          </mat-card-actions>
        </mat-card>

        <div class="features-preview">
          <h3>Why Choose Abid Ansari AI Assistant?</h3>
          <div class="feature-list">
            <div class="feature-item">
              <mat-icon>psychology</mat-icon>
              <span>Multiple AI Models (Gemini, Mistral, OpenAI)</span>
            </div>
            <div class="feature-item">
              <mat-icon>screenshot</mat-icon>
              <span>Advanced Screenshot Analysis</span>
            </div>
            <div class="feature-item">
              <mat-icon>security</mat-icon>
              <span>Secure API Key Management</span>
            </div>
            <div class="feature-item">
              <mat-icon>keyboard</mat-icon>
              <span>Hotkey Controls for Quick Access</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      padding-top: 64px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .login-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 48px;
      max-width: 1000px;
      width: 100%;
      padding: 24px;
      align-items: center;
    }

    .login-card {
      max-width: 400px;
      width: 100%;
      margin: 0 auto;
    }

    .login-header {
      text-align: center;
      padding-bottom: 24px;
    }

    .header-content {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .login-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #3f51b5;
      margin-bottom: 16px;
    }

    .login-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .full-width {
      width: 100%;
    }

    .form-actions {
      margin-top: 16px;
    }

    .login-button {
      width: 100%;
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      position: relative;
    }

    .button-spinner {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    .login-actions {
      padding-top: 16px;
    }

    .action-links {
      text-align: center;
      width: 100%;
    }

    .action-links p {
      margin-bottom: 8px;
      color: #666;
    }

    .link {
      color: #3f51b5;
      text-decoration: none;
      font-weight: 500;
    }

    .link:hover {
      text-decoration: underline;
    }

    .features-preview {
      background: white;
      padding: 32px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .features-preview h3 {
      color: #333;
      margin-bottom: 24px;
      font-size: 1.5rem;
      text-align: center;
    }

    .feature-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .feature-item mat-icon {
      color: #3f51b5;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .feature-item span {
      color: #333;
      font-weight: 500;
    }

    @media (max-width: 768px) {
      .login-content {
        grid-template-columns: 1fr;
        gap: 24px;
        padding: 16px;
      }

      .features-preview {
        order: -1;
      }

      .login-card {
        max-width: 100%;
      }
    }
  `]
})
export class LoginComponent {
  loginForm: FormGroup;
  isLoading = false;
  hidePassword = true;

  constructor(
    private fb: FormBuilder,
    private firebaseService: FirebaseService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }

  async onSubmit(): Promise<void> {
    if (this.loginForm.valid && !this.isLoading) {
      this.isLoading = true;
      
      try {
        const { email, password } = this.loginForm.value;
        const result = await this.firebaseService.login(email, password);
        
        if (result.success) {
          this.snackBar.open('Login successful!', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard']);
        } else {
          this.snackBar.open(result.message, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      } catch (error) {
        this.snackBar.open('An error occurred during login', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      } finally {
        this.isLoading = false;
      }
    }
  }
}
