#!/usr/bin/env python3
"""
Build script for Abid Ansari AI Assistant
Creates a standalone executable with all dependencies
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    """Main build function"""
    print("🔨 Building Abid Ansari AI Assistant...")
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed")
    
    # Build configuration
    app_name = "AbidAnsariAI"
    main_script = "abidansari.py"
    output_dir = "dist"
    build_dir = "build"
    
    # Clean previous builds
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
        print("🧹 Cleaned previous build")
    
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
        print("🧹 Cleaned build directory")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--name", app_name,
        "--onefile",
        "--windowed",
        "--icon=icon.ico",  # Add icon if available
        "--add-data", "firebase_service_account.json;.",
        "--hidden-import", "google.generativeai",
        "--hidden-import", "mistralai",
        "--hidden-import", "openai",
        "--hidden-import", "firebase_admin",
        "--hidden-import", "PyQt5",
        "--hidden-import", "pynput",
        "--hidden-import", "PIL",
        "--hidden-import", "requests",
        "--hidden-import", "ctypes",
        "--collect-all", "google.generativeai",
        "--collect-all", "mistralai",
        "--collect-all", "firebase_admin",
        "--collect-all", "PyQt5",
        main_script
    ]
    
    print(f"🚀 Building {app_name}...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # Run PyInstaller
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Build completed successfully!")
        
        # Copy to website assets
        exe_path = Path(output_dir) / f"{app_name}.exe"
        website_assets = Path("website/src/assets/downloads")
        
        if exe_path.exists():
            # Create assets directory if it doesn't exist
            website_assets.mkdir(parents=True, exist_ok=True)
            
            # Copy executable to website assets
            target_path = website_assets / f"{app_name}-Setup.exe"
            shutil.copy2(exe_path, target_path)
            print(f"✅ Copied executable to {target_path}")
            
            # Get file size
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print(f"📦 Executable size: {file_size:.1f} MB")
            
        else:
            print("❌ Executable not found after build")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False
    
    print("🎉 Build process completed!")
    return True

def create_installer():
    """Create an installer using NSIS or Inno Setup (optional)"""
    print("📦 Creating installer...")
    
    # This would require NSIS or Inno Setup to be installed
    # For now, we'll just use the standalone executable
    print("ℹ️ Using standalone executable (installer creation skipped)")

if __name__ == "__main__":
    success = main()
    if success:
        create_installer()
    else:
        sys.exit(1)
