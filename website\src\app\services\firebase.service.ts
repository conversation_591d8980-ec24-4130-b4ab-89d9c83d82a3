import { Injectable } from '@angular/core';
import { Auth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, User } from '@angular/fire/auth';
import { Firestore, doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs } from '@angular/fire/firestore';
import { Observable, BehaviorSubject } from 'rxjs';

export interface UserData {
  uid: string;
  email: string;
  displayName?: string;
  timeLimit: number;
  timeUsage: number;
  deviceId?: string;
  lastLogin: string;
  apiKeys?: {
    gemini?: string;
    mistral?: string;
    openai?: string;
    openrouter?: string;
  };
  subscription?: {
    active: boolean;
    plan: string;
    expiresAt: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private auth: Auth,
    private firestore: Firestore
  ) {
    // Listen to auth state changes
    this.auth.onAuthStateChanged(user => {
      this.currentUserSubject.next(user);
    });
  }

  // Authentication methods
  async login(email: string, password: string): Promise<{ success: boolean; message: string; user?: User }> {
    try {
      const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
      await this.updateLastLogin(userCredential.user.uid);
      return { 
        success: true, 
        message: 'Login successful', 
        user: userCredential.user 
      };
    } catch (error: any) {
      return { 
        success: false, 
        message: error.message || 'Login failed' 
      };
    }
  }

  async register(email: string, password: string, displayName?: string): Promise<{ success: boolean; message: string; user?: User }> {
    try {
      const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
      
      // Create user document in Firestore
      await this.createUserDocument(userCredential.user, displayName);
      
      return { 
        success: true, 
        message: 'Registration successful', 
        user: userCredential.user 
      };
    } catch (error: any) {
      return { 
        success: false, 
        message: error.message || 'Registration failed' 
      };
    }
  }

  async logout(): Promise<void> {
    await signOut(this.auth);
  }

  // User data methods
  async createUserDocument(user: User, displayName?: string): Promise<void> {
    const userRef = doc(this.firestore, 'users', user.uid);
    const userData: UserData = {
      uid: user.uid,
      email: user.email!,
      displayName: displayName || user.displayName || '',
      timeLimit: 300, // 5 minutes default
      timeUsage: 0,
      lastLogin: new Date().toISOString(),
      apiKeys: {},
      subscription: {
        active: false,
        plan: 'free',
        expiresAt: ''
      }
    };
    
    await setDoc(userRef, userData);
  }

  async getUserData(uid: string): Promise<UserData | null> {
    try {
      const userRef = doc(this.firestore, 'users', uid);
      const userSnap = await getDoc(userRef);
      
      if (userSnap.exists()) {
        return userSnap.data() as UserData;
      }
      return null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  async updateUserApiKeys(uid: string, apiKeys: any): Promise<boolean> {
    try {
      const userRef = doc(this.firestore, 'users', uid);
      await updateDoc(userRef, { apiKeys });
      return true;
    } catch (error) {
      console.error('Error updating API keys:', error);
      return false;
    }
  }

  async updateSubscription(uid: string, subscription: any): Promise<boolean> {
    try {
      const userRef = doc(this.firestore, 'users', uid);
      await updateDoc(userRef, { subscription });
      return true;
    } catch (error) {
      console.error('Error updating subscription:', error);
      return false;
    }
  }

  private async updateLastLogin(uid: string): Promise<void> {
    try {
      const userRef = doc(this.firestore, 'users', uid);
      await updateDoc(userRef, { lastLogin: new Date().toISOString() });
    } catch (error) {
      console.error('Error updating last login:', error);
    }
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.getCurrentUser() !== null;
  }

  // Additional methods for enhanced functionality
  async getCurrentUserData(): Promise<UserData | null> {
    const user = this.getCurrentUser();
    if (!user) return null;
    return await this.getUserData(user.uid);
  }

  async updateUserProfile(uid: string, data: Partial<UserData>): Promise<void> {
    const userRef = doc(this.firestore, 'users', uid);
    await updateDoc(userRef, data);
  }
}
